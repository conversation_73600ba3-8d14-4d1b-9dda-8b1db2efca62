import { logger } from '@hopfun/logger';
import type {
  EventId,
  PaginatedEvents,
  SuiEvent,
  SuiEventFilter,
} from '@mysten/sui/client';
import { SuiClient } from '@mysten/sui/client';
import { EventEmitter } from 'events';

import type { Network } from '@/config/environment';
import { env, getNetworkConfig } from '@/config/environment';

export interface SuiEventSubscription {
  filter: SuiEventFilter;
  onEvent: (event: SuiEvent) => Promise<void>;
  onError?: (error: Error) => void;
  lastEventId?: EventId;
}

export class SuiClientManager extends EventEmitter {
  private client: SuiClient;
  private network: Network;
  private subscriptions = new Map<string, SuiEventSubscription>();
  private isConnected = false;
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts: number;
  private readonly reconnectDelayMs: number;
  private pollingInterval?: NodeJS.Timeout;

  constructor(network: Network = env.NETWORK) {
    super();
    this.network = network;
    this.maxReconnectAttempts = env.WS_RECONNECT_ATTEMPTS;
    this.reconnectDelayMs = env.WS_RECONNECT_DELAY_MS;

    const config = getNetworkConfig(network);
    this.client = new SuiClient({ url: config.rpcUrl });

    logger.info(
      {
        network,
        rpcUrl: config.rpcUrl,
      },
      `Initializing Sui client for network: ${network}`,
    );
  }

  async connect(): Promise<void> {
    try {
      // Test connection
      await this.client.getLatestCheckpointSequenceNumber();
      this.isConnected = true;
      this.reconnectAttempts = 0;

      logger.info(
        {
          network: this.network,
        },
        'Sui client connected successfully',
      );

      this.emit('connected');

      // Start event polling if we have subscriptions
      if (this.subscriptions.size > 0) {
        this.startEventPolling();
      }
    } catch (error) {
      this.isConnected = false;
      logger.error(
        {
          network: this.network,
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to connect to Sui client',
      );

      this.emit('disconnected', error);
      throw error;
    }
  }

  disconnect(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = undefined;
    }

    this.isConnected = false;
    this.subscriptions.clear();

    logger.info(
      {
        network: this.network,
      },
      'Sui client disconnected',
    );

    this.emit('disconnected');
  }

  subscribe(id: string, subscription: SuiEventSubscription): void {
    this.subscriptions.set(id, subscription);

    logger.info(
      {
        id,
        filter: subscription.filter,
        network: this.network,
      },
      'Added event subscription',
    );

    // Start polling if not already running and we're connected
    if (this.isConnected && !this.pollingInterval) {
      this.startEventPolling();
    }
  }

  unsubscribe(id: string): void {
    if (this.subscriptions.delete(id)) {
      logger.info({ id, network: this.network }, 'Removed event subscription');

      // Stop polling if no more subscriptions
      if (this.subscriptions.size === 0 && this.pollingInterval) {
        clearInterval(this.pollingInterval);
        this.pollingInterval = undefined;
        logger.info({}, 'Stopped event polling - no active subscriptions');
      }
    }
  }

  private startEventPolling(): void {
    if (this.pollingInterval) {
      return; // Already polling
    }

    logger.info(
      {
        network: this.network,
        subscriptions: this.subscriptions.size,
        delayMs: env.PROCESSING_DELAY_MS,
      },
      'Starting event polling',
    );

    this.pollingInterval = setInterval(async () => {
      if (!this.isConnected) {
        return;
      }

      try {
        await this.pollEvents();
      } catch (error) {
        logger.error(
          {
            network: this.network,
            error: error instanceof Error ? error.message : String(error),
          },
          'Error during event polling',
        );

        // Try to reconnect on persistent errors
        if (error instanceof Error && error.message.includes('network')) {
          await this.handleReconnect();
        }
      }
    }, env.PROCESSING_DELAY_MS);
  }

  private async pollEvents(): Promise<void> {
    for (const [id, subscription] of this.subscriptions) {
      try {
        // Query events - for now, don't use cursor as it's causing issues
        const events = await this.queryEvents(
          subscription.filter,
          undefined // TODO: Fix cursor handling for lastEventId
        );

        if (events.data.length > 0) {
          logger.info(
            {
              subscriptionId: id,
              eventCount: events.data.length,
              firstEvent: events.data[0].type,
              network: this.network,
            },
            'Found new events for subscription',
          );

          for (const event of events.data) {
            try {
              await subscription.onEvent(event);
              // Update last event ID after successful processing
              subscription.lastEventId = event.id;
            } catch (error) {
              logger.error(
                {
                  subscriptionId: id,
                  eventId: event.id,
                  eventType: event.type,
                  error: error instanceof Error ? error.message : String(error),
                },
                'Error processing event in subscription',
              );

              if (subscription.onError) {
                subscription.onError(
                  error instanceof Error ? error : new Error(String(error)),
                );
              }
            }
          }
        }
      } catch (error) {
        logger.error(
          {
            subscriptionId: id,
            filter: subscription.filter,
            error: error instanceof Error ? error.message : String(error),
          },
          'Error querying events for subscription',
        );

        if (subscription.onError) {
          subscription.onError(
            error instanceof Error ? error : new Error(String(error)),
          );
        }
      }
    }
  }

  private async handleReconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error(
        {
          network: this.network,
          attempts: this.reconnectAttempts,
        },
        'Max reconnect attempts reached',
      );
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    logger.info(
      {
        network: this.network,
        attempt: this.reconnectAttempts,
        maxAttempts: this.maxReconnectAttempts,
      },
      'Attempting to reconnect',
    );

    // Stop current polling
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = undefined;
    }

    // Wait before reconnecting
    await new Promise((resolve) => setTimeout(resolve, this.reconnectDelayMs));

    try {
      await this.connect();
    } catch (error) {
      logger.error(
        {
          network: this.network,
          attempt: this.reconnectAttempts,
          error: error instanceof Error ? error.message : String(error),
        },
        'Reconnect attempt failed',
      );

      // Schedule another reconnect attempt
      setTimeout(() => this.handleReconnect(), this.reconnectDelayMs);
    }
  }

  // Sui Client Methods
  async queryEvents(
    filter: SuiEventFilter,
    cursor?: string,
    limit?: number,
  ): Promise<PaginatedEvents> {
    if (!this.isConnected) {
      throw new Error('Sui client not connected');
    }

    return await this.client.queryEvents({
      query: filter,
      cursor: cursor as EventId | undefined,
      limit: limit ?? env.BATCH_SIZE,
      order: 'ascending',
    });
  }

  async getCheckpoint(id: string) {
    if (!this.isConnected) {
      throw new Error('Sui client not connected');
    }

    return await this.client.getCheckpoint({ id });
  }

  async getLatestCheckpointSequenceNumber(): Promise<string> {
    if (!this.isConnected) {
      throw new Error('Sui client not connected');
    }

    return await this.client.getLatestCheckpointSequenceNumber();
  }

  async getTransaction(digest: string) {
    if (!this.isConnected) {
      throw new Error('Sui client not connected');
    }

    return await this.client.getTransactionBlock({
      digest,
      options: {
        showInput: true,
        showEffects: true,
        showEvents: true,
        showObjectChanges: true,
        showBalanceChanges: true,
      },
    });
  }

  // Getters
  get isClientConnected(): boolean {
    return this.isConnected;
  }

  get currentNetwork(): Network {
    return this.network;
  }

  get activeSubscriptions(): number {
    return this.subscriptions.size;
  }

  getSuiClient(): SuiClient {
    return this.client;
  }
}

// Singleton instance
let suiClientInstance: SuiClientManager | null = null;

export function getSuiClient(network?: Network): SuiClientManager {
  if (
    !suiClientInstance ||
    (network && suiClientInstance.currentNetwork !== network)
  ) {
    suiClientInstance = new SuiClientManager(network);
  }
  return suiClientInstance;
}

export default SuiClientManager;
