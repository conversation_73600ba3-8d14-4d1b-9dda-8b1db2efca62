import { createPrismaClient } from '@hopfun/database';
import { logger } from '@hopfun/logger';
import { ObjectId } from 'mongodb';

import {
  env,
  getNetworkConfig,
  validateNetworkConfig,
} from './config/environment';
import { IndexerHttpServer } from './http-server';
import BondingCurveBuyProcessor from './processors/bonding-curve-buy';
import BondingCurveCompleteProcessor from './processors/bonding-curve-complete';
import BondingCurveCreatedProcessor from './processors/bonding-curve-created';
import BondingCurveMigrateProcessor from './processors/bonding-curve-migrate';
import BondingCurveSellProcessor from './processors/bonding-curve-sell';
// Import all event processors
import ConnectorCreatedProcessor from './processors/connector-created';
import { EventProcessingEngine } from './processors/engine';
import { AggregationService } from './services/aggregation';
import { getSuiClient } from './sui/client';
import { HopfunEventFilters } from './sui/events';

export class HopfunIndexer {
  private suiClient = getSuiClient(env.NETWORK);
  private processingEngine: EventProcessingEngine;
  private eventFilters: HopfunEventFilters;
  private aggregationService: AggregationService;
  private httpServer: IndexerHttpServer;
  private db = createPrismaClient(env.DATABASE_URL);
  private isRunning = false;
  private shutdownInProgress = false;

  constructor() {
    // Validate configuration
    validateNetworkConfig(env.NETWORK);

    this.eventFilters = new HopfunEventFilters();

    this.processingEngine = new EventProcessingEngine({
      network: env.NETWORK,
      concurrency: 5,
    });

    this.aggregationService = new AggregationService(env.NETWORK);
    this.httpServer = new IndexerHttpServer(this);

    this.setupProcessors();
    this.setupEventHandlers();

    logger.info(
      {
        network: env.NETWORK,
        nodeEnv: env.NODE_ENV,
      },
      'HopFun Indexer initialized',
    );
  }

  private setupProcessors(): void {
    // Register all event processors
    this.processingEngine.registerProcessor(
      'ConnectorCreated',
      new ConnectorCreatedProcessor(env.NETWORK),
    );

    this.processingEngine.registerProcessor(
      'BondingCurveCreated',
      new BondingCurveCreatedProcessor(env.NETWORK),
    );

    this.processingEngine.registerProcessor(
      'BondingCurveBuy',
      new BondingCurveBuyProcessor(env.NETWORK),
    );

    this.processingEngine.registerProcessor(
      'BondingCurveSell',
      new BondingCurveSellProcessor(env.NETWORK),
    );

    this.processingEngine.registerProcessor(
      'BondingCurveComplete',
      new BondingCurveCompleteProcessor(env.NETWORK),
    );

    this.processingEngine.registerProcessor(
      'BondingCurveMigrate',
      new BondingCurveMigrateProcessor(env.NETWORK),
    );

    logger.info({}, 'All event processors registered');
  }

  private setupEventHandlers(): void {
    // Sui Client Events
    this.suiClient.on('connected', () => {
      logger.info({}, 'Sui client connected');
    });

    this.suiClient.on('disconnected', (error) => {
      logger.warn({ error }, 'Sui client disconnected');
    });

    this.suiClient.on('maxReconnectAttemptsReached', () => {
      logger.error({}, 'Max reconnect attempts reached, shutting down');
      void this.shutdown();
    });

    // Processing Engine Events
    this.processingEngine.on('eventProcessed', (data) => {
      logger.debug(data, 'Event processed successfully');
    });

    this.processingEngine.on('eventFailed', (data) => {
      logger.warn(data, 'Event processing failed');
    });

    this.processingEngine.on('eventError', (data) => {
      logger.error(data, 'Event processing error');
    });

    this.processingEngine.on('metricsUpdated', (metrics) => {
      logger.info(metrics, 'Processing metrics updated');
    });

    // Process shutdown signals
    process.on('SIGINT', () => {
      void this.shutdown();
    });
    process.on('SIGTERM', () => {
      void this.shutdown();
    });
    process.on('uncaughtException', (error) => {
      logger.error(
        {
          error: error.message,
          stack: error.stack,
        },
        'Uncaught exception',
      );
      void this.shutdown();
    });
    process.on('unhandledRejection', (reason) => {
      logger.error({ reason }, 'Unhandled rejection');
      void this.shutdown();
    });
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn({}, 'Indexer already running');
      return;
    }

    try {
      logger.info({}, 'Starting HopFun Indexer...');

      // Initialize indexer state
      await this.initializeIndexerState();

      // Connect to Sui network
      await this.suiClient.connect();

      // Start HTTP server
      await this.httpServer.start();

      // Start processing engine
      this.processingEngine.start();

      // Start aggregation service
      this.aggregationService.start();

      // Subscribe to all relevant events
      this.subscribeToEvents();

      this.isRunning = true;

      logger.info(
        {
          network: env.NETWORK,
          subscriptions: this.suiClient.activeSubscriptions,
          httpPort: env.HEALTH_CHECK_PORT,
        },
        'HopFun Indexer started successfully',
      );
    } catch (error) {
      logger.error(
        {
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to start indexer',
      );

      await this.shutdown();
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      logger.warn({}, 'Indexer not running');
      return;
    }

    await this.shutdown();
  }

  private async shutdown(): Promise<void> {
    if (this.shutdownInProgress) {
      return;
    }

    this.shutdownInProgress = true;
    logger.info({}, 'Shutting down HopFun Indexer...');

    try {
      // Stop aggregation service
      this.aggregationService.stop();

      // Stop processing engine
      await this.processingEngine.stop();

      // Stop HTTP server
      await this.httpServer.stop();

      // Disconnect from Sui client
      this.suiClient.disconnect();

      // Close database connection
      await this.db.$disconnect();

      this.isRunning = false;

      logger.info({}, 'HopFun Indexer shut down successfully');
    } catch (error) {
      logger.error(
        {
          error: error instanceof Error ? error.message : String(error),
        },
        'Error during shutdown',
      );
    } finally {
      process.exit(0);
    }
  }

  private async initializeIndexerState(): Promise<void> {
    try {
      logger.info(
        {
          databaseUrl: env.DATABASE_URL.replace(
            /\/\/[^:]+:[^@]+@/,
            '//***:***@',
          ), // Hide credentials
          network: env.NETWORK,
        },
        'Connecting to database',
      );

      // Test database connection first
      await this.db.$connect();
      logger.info({}, 'Database connected successfully');

      // Test basic database operation first
      logger.info({}, 'Testing database operations...');

      // Try to create or update indexer state
      // For development, we'll use a simple approach that doesn't require transactions
      let result;
      try {
        // First try to find existing state
        const existingState = await this.db.indexerState.findUnique({
          where: { network: env.NETWORK },
        });

        if (existingState) {
          // Update existing state
          result = await this.db.indexerState.update({
            where: { network: env.NETWORK },
            data: {
              isHealthy: true,
              lastError: null,
            },
          });
          logger.info({}, 'Updated existing indexer state');
        } else {
          // Create new state with proper ObjectId
          result = await this.db.indexerState.create({
            data: {
              id: new ObjectId().toString(),
              network: env.NETWORK,
              isHealthy: true,
            },
          });
          logger.info({}, 'Created new indexer state');
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        // If we get a replica set error, log a helpful message
        if (error.message?.includes('replica set')) {
          logger.error(
            { error: error.message },
            'MongoDB replica set required. Please run: docker-compose up -d to start MongoDB with replica set configuration',
          );
          throw new Error(
            'MongoDB replica set required. Run "docker-compose up -d" to start MongoDB with proper configuration.',
          );
        }
        throw error;
      }

      logger.info(
        { id: result.id, network: result.network },
        'Indexer state operation result',
      );

      logger.info({ network: env.NETWORK }, 'Indexer state initialized');
    } catch (error) {
      logger.error(
        {
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          databaseUrl: env.DATABASE_URL.replace(
            /\/\/[^:]+:[^@]+@/,
            '//***:***@',
          ), // Hide credentials
        },
        'Failed to initialize indexer state',
      );
      throw error;
    }
  }

  private subscribeToEvents(): void {
    try {
      const config = getNetworkConfig(env.NETWORK);
      const hopfunPackageId = config.packagesIds.hopfun;

      // Subscribe to specific event types that we know work
      const eventSubscriptions = [
        {
          name: 'connector-created',
          filter: {
            MoveEventType: `${hopfunPackageId}::events::ConnectorCreated`,
          },
        },
        // For BondingCurveCreated events with type parameters, we use the All filter and filter in code
        {
          name: 'all-events-filtered',
          filter: { All: [] as [] },
          filterInCode: true, // Flag to indicate we need to filter in application code
        },
      ];

      for (const subscription of eventSubscriptions) {
        const { name, filter, filterInCode } = subscription;
        this.suiClient.subscribe(`hopfun-${name}`, {
          filter,
          onEvent: async (event) => {
            try {
              // If this subscription requires filtering in code, check if it's a HopFun event
              if (filterInCode) {
                const isHopfunEvent =
                  event.packageId === hopfunPackageId ||
                  event.type.includes(`${hopfunPackageId}::events::`);

                if (!isHopfunEvent) {
                  return; // Skip non-HopFun events
                }
              }

              logger.info('Processing HopFun event', {
                subscription: `hopfun-${name}`,
                eventType: event.type,
                eventId: event.id,
                packageId: event.packageId,
              });

              const result = await this.processingEngine.processEvent(event);
              logger.info('Event processing result', {
                eventId: event.id,
                eventType: event.type,
                success: result.success,
                error: result.error,
                data: result.data,
              });
            } catch (error) {
              logger.error(
                {
                  eventId: event.id,
                  eventType: event.type,
                  error: error instanceof Error ? error.message : String(error),
                },
                'Failed to process event',
              );
            }
          },
          onError: (error) => {
            logger.error(
              {
                subscription: `hopfun-${name}`,
                error: error.message,
              },
              'Event subscription error',
            );
          },
        });

        logger.info(
          {
            subscription: `hopfun-${name}`,
            filter,
            network: env.NETWORK,
          },
          'Subscribed to HopFun events',
        );
      }

      logger.info(
        {
          network: env.NETWORK,
        },
        'Subscribed to HopFun events',
      );
    } catch (error) {
      logger.error(
        {
          error: error instanceof Error ? error.message : String(error),
        },
        'Failed to subscribe to events',
      );
      throw error;
    }
  }

  // Health check endpoint
  getHealthStatus() {
    return {
      isRunning: this.isRunning,
      network: env.NETWORK,
      suiClientConnected: this.suiClient.isClientConnected,
      activeSubscriptions: this.suiClient.activeSubscriptions,
      processingMetrics: this.processingEngine.getMetrics(),
      queueStatus: this.processingEngine.getQueueStatus(),
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
    };
  }

  // Metrics endpoint
  getMetrics() {
    return {
      processing: this.processingEngine.getMetrics(),
      queue: this.processingEngine.getQueueStatus(),
      sui: {
        connected: this.suiClient.isClientConnected,
        network: this.suiClient.currentNetwork,
        subscriptions: this.suiClient.activeSubscriptions,
      },
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        nodeVersion: process.version,
      },
    };
  }
}

// Start the indexer if this file is run directly
// @ts-expect-error – TypeScript doesn't recognize import.meta.url in Node.js
if (import.meta.url === `file://${process.argv[1]}`) {
  const indexer = new HopfunIndexer();

  indexer.start().catch((error) => {
    logger.error({ error }, 'Failed to start indexer');
    process.exit(1);
  });
}

export default HopfunIndexer;
